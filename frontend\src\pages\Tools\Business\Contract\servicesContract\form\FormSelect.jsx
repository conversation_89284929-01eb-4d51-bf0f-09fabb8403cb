import React, { useState } from 'react';
import { FiInfo, FiChevronDown } from 'react-icons/fi';

const FormSelect = ({ 
  label, 
  name, 
  value, 
  onChange, 
  options, 
  placeholder, 
  tooltip, 
  icon: Icon, 
  required = false,
  className = ""
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label with Icon and Tooltip */}
      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2 text-sm font-medium text-slate-200">
          {Icon && <Icon className="w-4 h-4 text-blue-400" />}
          <span>{label}</span>
          {required && <span className="text-red-400">*</span>}
        </label>
        
        {tooltip && (
          <div className="relative">
            <button
              type="button"
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              className="text-slate-400 hover:text-blue-400 transition-colors"
            >
              <FiInfo className="w-4 h-4" />
            </button>
            
            {showTooltip && (
              <div className="absolute right-0 top-6 z-10 w-64 p-3 bg-slate-800 border border-slate-600 rounded-lg shadow-xl">
                <p className="text-xs text-slate-300">{tooltip}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Select Field */}
      <div className="relative">
        <select
          name={name}
          value={value}
          onChange={onChange}
          required={required}
          className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-all duration-200 appearance-none cursor-pointer"
        >
          <option value="" disabled className="text-slate-400">
            {placeholder || `Select ${label.toLowerCase()}`}
          </option>
          {options.map((option) => (
            <option key={option} value={option} className="bg-slate-800 text-white">
              {option}
            </option>
          ))}
        </select>
        
        {/* Custom dropdown arrow */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <FiChevronDown className="w-4 h-4 text-slate-400" />
        </div>
      </div>
    </div>
  );
};

export default FormSelect;

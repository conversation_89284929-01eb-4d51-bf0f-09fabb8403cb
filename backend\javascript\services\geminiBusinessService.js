// /backend/services/geminiBusinessService.js
import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';

dotenv.config();

// Initialize the core AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Centralize the model name and generation configuration
const modelName = process.env.GEMINI_MODEL_NAME || 'gemini-pro';
const generationConfig = {
    temperature: 0.7,
    topP: 1,
    topK: 1,
    maxOutputTokens: 28192, // High token limit for the large combined report
};

/**
 * A generic function to generate content from the Gemini API.
 * It takes a prompt and returns the generated text.
 * @param {string} prompt - The complete prompt to send to the AI.
 * @returns {Promise<string>} The AI-generated text content.
 */
export const generateContent = async (prompt) => {
    try {
        const model = genAI.getGenerativeModel({ model: modelName, generationConfig });

        // --- CONSOLE LOG TO VERIFY SINGLE API CALL ---
        // This log will appear in your terminal right before the expensive API call is made.
        // We log the model name and prompt length for context, without logging the entire (potentially large) prompt.
        console.log(`\n[GEMINI_SERVICE] --> Making a single request to Google AI.`);
        console.log(`    - Model: ${modelName}`);
        console.log(`    - Prompt Length: ${prompt.length} characters`);
        console.log(`--------------------------------------------------`);
        // --- END OF CONSOLE LOG ---

        const result = await model.generateContent(prompt);
        const response = await result.response;
        
        if (!response || !response.text()) {
            throw new Error('Received an empty response from the AI service.');
        }

        return response.text();
    } catch (error) {
        console.error('Gemini AI Service Error:', error);
        // Re-throw the error to be handled by the calling controller
        throw new Error('Failed to generate content from the AI service.');
    }
};
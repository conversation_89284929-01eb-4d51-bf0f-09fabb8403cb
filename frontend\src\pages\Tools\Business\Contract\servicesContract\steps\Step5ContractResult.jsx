import React, { useState } from 'react';
import { FiDownload, FiArrowLeft, FiLoader, FiCheckCircle, FiCopy, FiRefreshCw, FiFileText } from 'react-icons/fi';
import FormattedContractDisplay from '../format/FormattedContractDisplay';
import { downloadContractPDF } from '../pdf/contractPdfGenerator';

const Step5ContractResult = ({ 
  formData, 
  contractContent, 
  isLoading, 
  onBack, 
  onRegenerate,
  onReset 
}) => {
  const [copied, setCopied] = useState(false);
  const [downloadingPDF, setDownloadingPDF] = useState(false);

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(contractContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const handleDownloadPDF = async () => {
    try {
      setDownloadingPDF(true);
      await downloadContractPDF(contractContent, formData, formData.contractLanguage);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      // Could show error message to user
    } finally {
      setDownloadingPDF(false);
    }
  };

  const handleDownload = () => {
    const element = document.createElement('a');
    const file = new Blob([contractContent], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `service-contract-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  if (isLoading) {
    return (
      <div className="w-full max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full mb-4 backdrop-blur-sm border border-white/10">
            <FiLoader className="w-6 h-6 text-green-400 mr-2 animate-spin" />
            <span className="text-green-400 font-semibold">Step 5 of 5</span>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-green-200 to-emerald-200 bg-clip-text text-transparent mb-2">
            Generating Your Contract
          </h2>
          <p className="text-slate-400 max-w-2xl mx-auto">
            Our AI is crafting your professional service contract...
          </p>
        </div>

        <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-12">
          <div className="text-center">
            <div className="relative mb-8">
              <div className="w-20 h-20 mx-auto bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center">
                <FiLoader className="w-10 h-10 text-green-400 animate-spin" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full animate-pulse"></div>
            </div>
            
            <h3 className="text-2xl font-bold text-white mb-4">Creating Your Contract</h3>
            <p className="text-slate-400 mb-6 max-w-md mx-auto">
              Please wait while we generate a comprehensive service contract based on your specifications.
            </p>
            
            <div className="flex justify-center space-x-2 mb-8">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className="w-2 h-2 bg-green-400 rounded-full animate-bounce"
                  style={{ animationDelay: `${i * 0.2}s` }}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full mb-4 backdrop-blur-sm border border-white/10">
          <FiCheckCircle className="w-6 h-6 text-green-400 mr-2" />
          <span className="text-green-400 font-semibold">Step 5 of 5</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-green-200 to-emerald-200 bg-clip-text text-transparent mb-2">
          Your Service Contract
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          Your professional service contract has been generated successfully
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        <button
          onClick={handleDownload}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-green-500/25"
        >
          <FiDownload className="w-4 h-4" />
          <span>Download Text</span>
        </button>

        <button
          onClick={handleDownloadPDF}
          disabled={downloadingPDF}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-200 shadow-lg ${
            downloadingPDF
              ? 'bg-gray-600 cursor-not-allowed'
              : 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-500 hover:to-pink-500 hover:shadow-red-500/25'
          } text-white`}
        >
          {downloadingPDF ? (
            <FiLoader className="w-4 h-4 animate-spin" />
          ) : (
            <FiFileText className="w-4 h-4" />
          )}
          <span>{downloadingPDF ? 'Generating PDF...' : 'Download PDF'}</span>
        </button>
        
        <button
          onClick={handleCopyToClipboard}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
            copied 
              ? 'bg-green-600 text-white' 
              : 'bg-slate-700 hover:bg-slate-600 text-white'
          }`}
        >
          <FiCopy className="w-4 h-4" />
          <span>{copied ? 'Copied!' : 'Copy Text'}</span>
        </button>
        
        <button
          onClick={onRegenerate}
          className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-semibold transition-all duration-200"
        >
          <FiRefreshCw className="w-4 h-4" />
          <span>Regenerate</span>
        </button>
      </div>

      {/* Contract Display */}
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 mb-8">
        <FormattedContractDisplay 
          content={contractContent} 
          language={formData.contractLanguage}
        />
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg font-semibold transition-all duration-200"
        >
          <FiArrowLeft className="w-4 h-4" />
          <span>Back to Edit</span>
        </button>
        
        <button
          onClick={onReset}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white rounded-lg font-semibold transition-all duration-200"
        >
          <span>Create New Contract</span>
        </button>
      </div>

      {/* Disclaimer */}
      <div className="mt-8 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
        <p className="text-yellow-200 text-sm text-center">
          <strong>Legal Disclaimer:</strong> This AI-generated contract is for informational purposes only. 
          Please have it reviewed by a qualified legal professional before use.
        </p>
      </div>
    </div>
  );
};

export default Step5ContractResult;

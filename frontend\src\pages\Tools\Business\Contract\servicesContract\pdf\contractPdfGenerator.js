// Contract PDF Generator
// Based on the existing business plan PDF generator but optimized for contracts

import jsPDF from 'jspdf';
import { parseContractContent } from '../format/contractContentFormatter';
import { loadFonts } from '../../../components/utils/fontLoader';
// Import modular formatters
import { renderContractHeader, renderContractSection, renderSubSection } from './formatters/headerFormatter';
import { renderContractParagraph, renderPlainText } from './formatters/paragraphFormatter';
import { renderContractListItem } from './formatters/listFormatter';
import { renderContractTable, renderPartiesTable } from './formatters/tableFormatter';
import { renderContractClause, renderContractTerm, renderSignatureBlock } from './formatters/clauseFormatter';
import { renderFinancialTerm } from './formatters/financialFormatter';

// PDF Configuration - Matching web display colors and styling
const PDF_CONFIG = {
  format: 'a4',
  orientation: 'portrait',
  unit: 'mm',
  margins: {
    top: 20,
    bottom: 20,
    left: 20,
    right: 20
  },
  colors: {
    primary: '#3b82f6',    // Blue (matching web headers)
    secondary: '#06b6d4',  // Cyan (matching web sections)
    accent: '#10b981',     // Green (matching web lists)
    text: '#1f2937',       // Dark gray (matching web text)
    light: '#6b7280',      // Light gray (matching web borders)
    financial: '#3b82f6',  // Blue for financial terms
    clause: '#f59e0b'      // Yellow for clauses
  },
  fonts: {
    regular: 'helvetica',  // Using standard fonts for better compatibility
    bold: 'helvetica',
    arabic: 'helvetica'
  },
  spacing: {
    lineHeight: 6,
    paragraphSpacing: 8,
    sectionSpacing: 15,
    listIndent: 20
  }
};

/**
 * Generate PDF from contract content
 * @param {string} contractContent - The contract content with tags
 * @param {Object} formData - Form data used to generate the contract
 * @param {string} language - Contract language for RTL support
 * @returns {Promise<jsPDF>} - The generated PDF document
 */
export const generateContractPDF = async (contractContent, formData, language = 'English') => {
  try {
    // Initialize PDF
    const doc = new jsPDF({
      orientation: PDF_CONFIG.orientation,
      unit: PDF_CONFIG.unit,
      format: PDF_CONFIG.format
    });

    // Load fonts
    await loadFonts(doc);

    // Set RTL support if needed
    const isRTL = ['Arabic', 'Hebrew', 'Persian', 'Urdu'].includes(language);
    if (isRTL) {
      doc.internal.isRtl = true;
    }

    // Parse contract content
    const parsedElements = parseContractContent(contractContent);

    // Set initial position
    let currentY = PDF_CONFIG.margins.top;
    const pageHeight = doc.internal.pageSize.getHeight();
    const maxY = pageHeight - PDF_CONFIG.margins.bottom;

    // Add contract metadata
    doc.setProperties({
      title: `Service Contract - ${formData.providerName || 'Provider'} & ${formData.clientName || 'Client'}`,
      subject: 'Service Agreement Contract',
      author: formData.providerName || 'Contract Generator',
      creator: 'AI Contract Generator',
      producer: 'Legal Contract Generator'
    });

    // Render each element
    for (let i = 0; i < parsedElements.length; i++) {
      const element = parsedElements[i];

      // Check if we need a new page
      if (currentY > maxY - 30) {
        doc.addPage();
        currentY = PDF_CONFIG.margins.top;
      }

      switch (element.type) {
        case 'header':
          currentY = renderContractHeader(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'section':
          currentY = renderContractSection(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'subsection':
          currentY = renderContractSection(doc, element.content, currentY, PDF_CONFIG, true);
          break;
        case 'paragraph':
          currentY = renderContractParagraph(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'listItem':
          currentY = renderContractListItem(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'clause':
          currentY = renderContractClause(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'term':
          currentY = renderContractTerm(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'financial':
          currentY = renderFinancialTerm(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'table':
          currentY = renderContractTable(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'signature':
          currentY = renderSignatureBlock(doc, element.content, currentY, PDF_CONFIG);
          break;
        case 'text':
          currentY = renderPlainText(doc, element.content, currentY, PDF_CONFIG);
          break;
        default:
          // Handle unknown types as paragraphs
          currentY = renderContractParagraph(doc, element.content, currentY, PDF_CONFIG);
          break;
      }

      // Add some spacing between elements
      currentY += 5;
    }

    // Add footer with generation info
    addContractFooter(doc, formData, language);

    return doc;
  } catch (error) {
    console.error('Error generating contract PDF:', error);
    throw new Error('Failed to generate PDF: ' + error.message);
  }
};

/**
 * Add footer to contract PDF
 * @param {jsPDF} doc - PDF document
 * @param {Object} formData - Form data
 * @param {string} language - Contract language
 */
const addContractFooter = (doc, formData, language) => {
  const pageCount = doc.internal.getNumberOfPages();
  const pageHeight = doc.internal.pageSize.getHeight();
  const pageWidth = doc.internal.pageSize.getWidth();

  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    
    // Set footer font
    doc.setFont(PDF_CONFIG.fonts.regular);
    doc.setFontSize(8);
    doc.setTextColor(PDF_CONFIG.colors.light);

    // Add page number
    const pageText = `Page ${i} of ${pageCount}`;
    const pageTextWidth = doc.getTextWidth(pageText);
    doc.text(pageText, pageWidth - PDF_CONFIG.margins.right - pageTextWidth, pageHeight - 10);

    // Add generation info on first page
    if (i === 1) {
      const generationText = `Generated on ${new Date().toLocaleDateString()} by AI Contract Generator`;
      doc.text(generationText, PDF_CONFIG.margins.left, pageHeight - 10);
    }
  }
};

/**
 * Download contract as PDF
 * @param {string} contractContent - The contract content
 * @param {Object} formData - Form data
 * @param {string} language - Contract language
 */
export const downloadContractPDF = async (contractContent, formData, language = 'English') => {
  try {
    const doc = await generateContractPDF(contractContent, formData, language);
    
    // Generate filename
    const providerName = (formData.providerName || 'Provider').replace(/[^a-zA-Z0-9]/g, '_');
    const clientName = (formData.clientName || 'Client').replace(/[^a-zA-Z0-9]/g, '_');
    const date = new Date().toISOString().split('T')[0];
    const filename = `Service_Contract_${providerName}_${clientName}_${date}.pdf`;
    
    // Download the PDF
    doc.save(filename);
    
    console.log(`[CONTRACT_PDF] Downloaded: ${filename}`);
  } catch (error) {
    console.error('Error downloading contract PDF:', error);
    throw error;
  }
};

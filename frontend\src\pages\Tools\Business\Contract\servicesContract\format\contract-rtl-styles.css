/* RTL Support for Contract Display */

/* RTL Typography and Layout */
.contract-display.rtl {
  direction: rtl;
  text-align: right;
}

.contract-display.rtl h1,
.contract-display.rtl h2,
.contract-display.rtl h3,
.contract-display.rtl h4,
.contract-display.rtl h5,
.contract-display.rtl h6 {
  text-align: right;
}

.contract-display.rtl p {
  text-align: right;
}

.contract-display.rtl ul,
.contract-display.rtl ol {
  padding-right: 1.5rem;
  padding-left: 0;
}

.contract-display.rtl li {
  text-align: right;
}

/* RTL Flex and Grid Adjustments */
.contract-display.rtl .flex {
  flex-direction: row-reverse;
}

.contract-display.rtl .flex-row {
  flex-direction: row-reverse;
}

/* RTL Border and Spacing */
.contract-display.rtl .border-l-4 {
  border-left: none;
  border-right: 4px solid;
}

.contract-display.rtl .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

.contract-display.rtl .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

.contract-display.rtl .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

.contract-display.rtl .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

/* Contract specific RTL adjustments */
.contract-display.rtl .signature-block {
  direction: rtl;
}

.contract-display.rtl .signature-block .flex {
  justify-content: space-between;
  flex-direction: row-reverse;
}

/* List item bullets for RTL */
.contract-display.rtl .contract-list-item {
  flex-direction: row-reverse;
  margin-right: 1.5rem;
  margin-left: 0;
  padding-right: 1rem;
  padding-left: 0;
}

.contract-display.rtl .contract-list-item .bullet {
  margin-left: 1rem;
  margin-right: 0;
}

/* Clause borders for RTL */
.contract-display.rtl .contract-clause {
  border-left: none;
  border-right: 4px solid;
}

/* Section headers for RTL */
.contract-display.rtl .section-header {
  flex-direction: row-reverse;
}

.contract-display.rtl .section-header .divider {
  margin-left: 0.75rem;
  margin-right: 0;
}

/* Paragraph indentation for RTL */
.contract-display.rtl p {
  padding-right: 1rem;
  padding-left: 0;
  margin-right: 0.5rem;
  margin-left: 0;
  border-right: 2px solid;
  border-left: none;
}

// PDF List Formatter
// Specialized formatting for contract lists with proper spacing

/**
 * Render contract list item with bullet and indentation
 * @param {jsPDF} doc - PDF document
 * @param {string} content - List item content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractListItem = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 25; // Extra margin for bullet and indentation

  // Set list font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(11);
  doc.setTextColor(config.colors.text);

  // Add bullet point (matching web display)
  const bulletX = isRTL 
    ? pageWidth - config.margins.right - 15
    : config.margins.left + 15;
  
  doc.setFillColor(config.colors.accent);
  doc.circle(bulletX, currentY - 1, 1.5, 'F');

  // Calculate text position with proper indentation (matching web display)
  const textX = isRTL 
    ? pageWidth - config.margins.right - 20
    : config.margins.left + 20;

  // Split text and calculate height
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 6;
  const totalHeight = lines.length * lineHeight + 4;

  // Add background for list item (matching web display)
  doc.setFillColor(250, 252, 250); // Very light green
  const rectX = isRTL 
    ? config.margins.left + 8
    : config.margins.left + 8;
  doc.rect(rectX, currentY - 4, maxWidth + 20, totalHeight, 'F');

  // Render text with proper spacing
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, textX, currentY, textOptions);

  return currentY + totalHeight + 6;
};

/**
 * Render multiple list items as a group
 * @param {jsPDF} doc - PDF document
 * @param {Array} items - Array of list item contents
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderListGroup = (doc, items, currentY, config) => {
  let yPosition = currentY;
  
  items.forEach((item, index) => {
    yPosition = renderContractListItem(doc, item, yPosition, config);
    
    // Add extra spacing between list items
    if (index < items.length - 1) {
      yPosition += 2;
    }
  });
  
  return yPosition + 5; // Extra spacing after the list group
};

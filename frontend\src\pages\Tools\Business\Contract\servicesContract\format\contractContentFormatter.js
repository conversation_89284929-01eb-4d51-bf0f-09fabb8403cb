// Contract content formatter using the established tag system
// Similar to business plan formatter but optimized for legal contracts

export const parseContractContent = (content) => {
  if (!content) return [];

  const lines = content.split('\n').filter(line => line.trim() !== '');
  const elements = [];

  let i = 0;
  while (i < lines.length) {
    const trimmedLine = lines[i].trim();

    if (trimmedLine.startsWith('~H~')) {
      // Main headers (contract sections)
      elements.push({
        type: 'header',
        content: trimmedLine.substring(3).trim(),
        key: `header-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~S~')) {
      // Section headers
      elements.push({
        type: 'section',
        content: trimmedLine.substring(3).trim(),
        key: `section-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~S_SUB~')) {
      // Sub-section headers
      elements.push({
        type: 'subsection',
        content: trimmedLine.substring(7).trim(),
        key: `subsection-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~P~')) {
      // Paragraphs
      elements.push({
        type: 'paragraph',
        content: trimmedLine.substring(3).trim(),
        key: `paragraph-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~L~')) {
      // List items
      elements.push({
        type: 'listItem',
        content: trimmedLine.substring(3).trim(),
        key: `list-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~CLAUSE~')) {
      // Special contract clauses
      elements.push({
        type: 'clause',
        content: trimmedLine.substring(8).trim(),
        key: `clause-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~TERM~')) {
      // Contract terms
      elements.push({
        type: 'term',
        content: trimmedLine.substring(6).trim(),
        key: `term-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~FINANCIAL~')) {
      // Financial terms
      elements.push({
        type: 'financial',
        content: trimmedLine.substring(11).trim(),
        key: `financial-${i}`
      });
      i++;
    } else if (trimmedLine.startsWith('~TABLE~')) {
      // Contract tables - handle multi-line table content
      const tableContent = [];

      // Add the first line content (if any) after ~TABLE~
      const firstLineContent = trimmedLine.substring(7).trim();
      if (firstLineContent) {
        tableContent.push(firstLineContent);
      }

      // Look ahead for table rows (lines that contain | and don't start with ~)
      let j = i + 1;
      while (j < lines.length) {
        const nextLine = lines[j].trim();

        // Stop if we hit another tag or empty line
        if (nextLine.startsWith('~') || nextLine === '') {
          break;
        }

        // Add lines that contain pipe characters (table rows)
        if (nextLine.includes('|')) {
          tableContent.push(nextLine);
        } else {
          // Stop if we hit a line without pipes (not part of table)
          break;
        }

        j++;
      }

      elements.push({
        type: 'table',
        content: tableContent.join('\n'),
        key: `table-${i}`
      });

      // Move index to after the table content
      i = j;
    } else if (trimmedLine.startsWith('~SIGNATURE~')) {
      // Signature blocks
      elements.push({
        type: 'signature',
        content: trimmedLine.substring(11).trim(),
        key: `signature-${i}`
      });
      i++;
    } else if (trimmedLine.length > 0 && !trimmedLine.startsWith('~')) {
      // Plain text (fallback)
      elements.push({
        type: 'text',
        content: trimmedLine,
        key: `text-${i}`
      });
      i++;
    } else {
      // Skip unrecognized tags
      i++;
    }
  }

  return elements;
};

// Helper function to detect RTL languages
export const isRTLLanguage = (language) => {
  const rtlLanguages = ['Arabic', 'Hebrew', 'Persian', 'Urdu'];
  return rtlLanguages.includes(language);
};

// Helper function to get text direction
export const getTextDirection = (language) => {
  return isRTLLanguage(language) ? 'rtl' : 'ltr';
};

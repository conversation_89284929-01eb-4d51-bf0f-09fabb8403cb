// Contract content formatter using the established tag system
// Similar to business plan formatter but optimized for legal contracts

export const parseContractContent = (content) => {
  if (!content) return [];
  
  const lines = content.split('\n').filter(line => line.trim() !== '');
  const elements = [];
  
  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    
    if (trimmedLine.startsWith('~H~')) {
      // Main headers (contract sections)
      elements.push({
        type: 'header',
        content: trimmedLine.substring(3).trim(),
        key: `header-${index}`
      });
    } else if (trimmedLine.startsWith('~S~')) {
      // Section headers
      elements.push({
        type: 'section',
        content: trimmedLine.substring(3).trim(),
        key: `section-${index}`
      });
    } else if (trimmedLine.startsWith('~S_SUB~')) {
      // Sub-section headers
      elements.push({
        type: 'subsection',
        content: trimmedLine.substring(7).trim(),
        key: `subsection-${index}`
      });
    } else if (trimmedLine.startsWith('~P~')) {
      // Paragraphs
      elements.push({
        type: 'paragraph',
        content: trimmedLine.substring(3).trim(),
        key: `paragraph-${index}`
      });
    } else if (trimmedLine.startsWith('~L~')) {
      // List items
      elements.push({
        type: 'listItem',
        content: trimmedLine.substring(3).trim(),
        key: `list-${index}`
      });
    } else if (trimmedLine.startsWith('~CLAUSE~')) {
      // Special contract clauses
      elements.push({
        type: 'clause',
        content: trimmedLine.substring(8).trim(),
        key: `clause-${index}`
      });
    } else if (trimmedLine.startsWith('~TERM~')) {
      // Contract terms
      elements.push({
        type: 'term',
        content: trimmedLine.substring(6).trim(),
        key: `term-${index}`
      });
    } else if (trimmedLine.startsWith('~FINANCIAL~')) {
      // Financial terms
      elements.push({
        type: 'financial',
        content: trimmedLine.substring(11).trim(),
        key: `financial-${index}`
      });
    } else if (trimmedLine.startsWith('~TABLE~')) {
      // Contract tables
      elements.push({
        type: 'table',
        content: trimmedLine.substring(7).trim(),
        key: `table-${index}`
      });
    } else if (trimmedLine.startsWith('~SIGNATURE~')) {
      // Signature blocks
      elements.push({
        type: 'signature',
        content: trimmedLine.substring(11).trim(),
        key: `signature-${index}`
      });
    } else if (trimmedLine.length > 0 && !trimmedLine.startsWith('~')) {
      // Plain text (fallback)
      elements.push({
        type: 'text',
        content: trimmedLine,
        key: `text-${index}`
      });
    }
  });
  
  return elements;
};

// Helper function to detect RTL languages
export const isRTLLanguage = (language) => {
  const rtlLanguages = ['Arabic', 'Hebrew', 'Persian', 'Urdu'];
  return rtlLanguages.includes(language);
};

// Helper function to get text direction
export const getTextDirection = (language) => {
  return isRTLLanguage(language) ? 'rtl' : 'ltr';
};

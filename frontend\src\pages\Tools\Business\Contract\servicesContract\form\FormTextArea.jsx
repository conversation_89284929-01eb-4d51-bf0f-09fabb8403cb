import React, { useState } from 'react';
import { FiInfo, FiEye, FiEyeOff } from 'react-icons/fi';

const FormTextArea = ({ 
  label, 
  name, 
  value, 
  onChange, 
  placeholder, 
  example, 
  tooltip, 
  icon: Icon, 
  required = false,
  rows = 4,
  className = ""
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [showExample, setShowExample] = useState(false);

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label with Icon and Tooltip */}
      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2 text-sm font-medium text-slate-200">
          {Icon && <Icon className="w-4 h-4 text-blue-400" />}
          <span>{label}</span>
          {required && <span className="text-red-400">*</span>}
        </label>
        
        {tooltip && (
          <div className="relative">
            <button
              type="button"
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              className="text-slate-400 hover:text-blue-400 transition-colors"
            >
              <FiInfo className="w-4 h-4" />
            </button>
            
            {showTooltip && (
              <div className="absolute right-0 top-6 z-10 w-64 p-3 bg-slate-800 border border-slate-600 rounded-lg shadow-xl">
                <p className="text-xs text-slate-300">{tooltip}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* TextArea Field */}
      <div className="relative">
        <textarea
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          required={required}
          rows={rows}
          className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-all duration-200 resize-vertical"
        />
      </div>

      {/* Example */}
      {example && (
        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={() => setShowExample(!showExample)}
            className="flex items-center space-x-1 text-xs text-slate-500 hover:text-slate-400 transition-colors"
          >
            {showExample ? <FiEyeOff className="w-3 h-3" /> : <FiEye className="w-3 h-3" />}
            <span>{showExample ? 'Hide' : 'Show'} Example</span>
          </button>
        </div>
      )}
      
      {showExample && example && (
        <div className="p-3 bg-slate-900/50 border border-slate-700 rounded text-xs text-slate-400 leading-relaxed">
          {example}
        </div>
      )}
    </div>
  );
};

export default FormTextArea;

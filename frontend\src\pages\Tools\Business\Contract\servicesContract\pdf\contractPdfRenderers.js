// Contract PDF Renderers
// Specialized rendering functions for different contract elements

/**
 * Render contract main header
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Header content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractHeader = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;

  // Set header font
  doc.setFont(config.fonts.bold);
  doc.setFontSize(18);
  doc.setTextColor(config.colors.primary);

  // Calculate text width and position
  const textWidth = doc.getTextWidth(content);
  const xPosition = isRTL 
    ? pageWidth - config.margins.right - textWidth
    : (pageWidth - textWidth) / 2; // Center align

  // Render header text
  doc.text(content, xPosition, currentY);

  // Add underline
  const underlineY = currentY + 2;
  const underlineStartX = isRTL 
    ? pageWidth - config.margins.right - textWidth - 10
    : xPosition - 10;
  const underlineEndX = isRTL 
    ? pageWidth - config.margins.right + 10
    : xPosition + textWidth + 10;

  doc.setDrawColor(config.colors.primary);
  doc.setLineWidth(1);
  doc.line(underlineStartX, underlineY, underlineEndX, underlineY);

  return currentY + 25;
};

/**
 * Render contract section header
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Section content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @param {boolean} isSubsection - Whether this is a subsection
 * @returns {number} - New Y position
 */
export const renderContractSection = (doc, content, currentY, config, isSubsection = false) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;

  // Set section font
  doc.setFont(config.fonts.bold);
  doc.setFontSize(isSubsection ? 12 : 14);
  doc.setTextColor(isSubsection ? config.colors.text : config.colors.secondary);

  // Calculate position
  const xPosition = isRTL 
    ? pageWidth - config.margins.right
    : config.margins.left;

  // Add section marker (colored bar)
  if (!isSubsection) {
    const barX = isRTL 
      ? pageWidth - config.margins.right + 2
      : config.margins.left - 5;
    
    doc.setFillColor(config.colors.secondary);
    doc.rect(barX, currentY - 4, 3, 8, 'F');
  }

  // Render section text
  const textOptions = isRTL ? { align: 'right' } : {};
  doc.text(content, xPosition, currentY, textOptions);

  // Add underline for main sections
  if (!isSubsection) {
    const textWidth = doc.getTextWidth(content);
    const underlineY = currentY + 2;
    const underlineStartX = isRTL 
      ? pageWidth - config.margins.right - textWidth
      : config.margins.left;
    const underlineEndX = isRTL 
      ? pageWidth - config.margins.right
      : config.margins.left + textWidth;

    doc.setDrawColor(config.colors.secondary);
    doc.setLineWidth(0.5);
    doc.line(underlineStartX, underlineY, underlineEndX, underlineY);
  }

  return currentY + (isSubsection ? 15 : 20);
};

/**
 * Render contract paragraph
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Paragraph content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractParagraph = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 10; // Extra margin for indentation

  // Set paragraph font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);

  // Calculate position with indentation
  const xPosition = isRTL 
    ? pageWidth - config.margins.right - 5
    : config.margins.left + 5;

  // Add background highlight
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 5;
  const totalHeight = lines.length * lineHeight + 4;

  doc.setFillColor(248, 250, 252); // Very light gray
  const rectX = isRTL 
    ? config.margins.left
    : config.margins.left + 2;
  doc.rect(rectX, currentY - 3, maxWidth + 8, totalHeight, 'F');

  // Add left border
  doc.setDrawColor(config.colors.light);
  doc.setLineWidth(2);
  const borderX = isRTL 
    ? pageWidth - config.margins.right - 2
    : config.margins.left + 2;
  doc.line(borderX, currentY - 3, borderX, currentY + totalHeight - 3);

  // Render text
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, xPosition, currentY, textOptions);

  return currentY + totalHeight + 5;
};

/**
 * Render contract list item
 * @param {jsPDF} doc - PDF document
 * @param {string} content - List item content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractListItem = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 20; // Extra margin for bullet

  // Set list font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);

  // Add bullet point
  const bulletX = isRTL 
    ? pageWidth - config.margins.right - 10
    : config.margins.left + 10;
  
  doc.setFillColor(config.colors.accent);
  doc.circle(bulletX, currentY - 1, 1, 'F');

  // Calculate text position
  const textX = isRTL 
    ? pageWidth - config.margins.right - 15
    : config.margins.left + 15;

  // Add background for list item
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 5;
  const totalHeight = lines.length * lineHeight + 2;

  doc.setFillColor(250, 252, 250); // Very light green
  const rectX = isRTL 
    ? config.margins.left + 5
    : config.margins.left + 5;
  doc.rect(rectX, currentY - 3, maxWidth + 15, totalHeight, 'F');

  // Render text
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, textX, currentY, textOptions);

  return currentY + totalHeight + 3;
};

/**
 * Render contract clause (highlighted section)
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Clause content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractClause = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 10;

  // Set clause font
  doc.setFont(config.fonts.bold);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);

  // Calculate dimensions
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 5;
  const totalHeight = lines.length * lineHeight + 8;

  // Add highlighted background
  doc.setFillColor(255, 251, 235); // Light yellow
  doc.rect(config.margins.left, currentY - 4, maxWidth + 10, totalHeight, 'F');

  // Add colored left border
  doc.setFillColor(245, 158, 11); // Yellow/orange
  const borderX = isRTL 
    ? pageWidth - config.margins.right - 4
    : config.margins.left;
  doc.rect(borderX, currentY - 4, 4, totalHeight, 'F');

  // Calculate text position
  const textX = isRTL 
    ? pageWidth - config.margins.right - 5
    : config.margins.left + 8;

  // Render text
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, textX, currentY, textOptions);

  return currentY + totalHeight + 5;
};

/**
 * Render signature block
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Signature content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderSignatureBlock = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;

  // Set signature font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);

  // Add dashed border
  doc.setDrawColor(config.colors.light);
  doc.setLineWidth(0.5);
  doc.setLineDashPattern([2, 2], 0);
  doc.rect(config.margins.left, currentY - 5, pageWidth - config.margins.left - config.margins.right, 40);
  doc.setLineDashPattern([], 0); // Reset dash pattern

  // Render content
  const textX = isRTL 
    ? pageWidth - config.margins.right - 5
    : config.margins.left + 5;
  const textOptions = isRTL ? { align: 'right' } : {};
  doc.text(content, textX, currentY + 5, textOptions);

  // Add signature lines
  const lineY = currentY + 25;
  const lineWidth = 60;
  
  // Provider signature
  const providerX = isRTL 
    ? pageWidth - config.margins.right - lineWidth
    : config.margins.left + 10;
  doc.setDrawColor(config.colors.text);
  doc.line(providerX, lineY, providerX + lineWidth, lineY);
  doc.setFontSize(8);
  doc.text('Provider Signature', providerX, lineY + 5, textOptions);

  // Client signature
  const clientX = isRTL 
    ? config.margins.left + 10
    : pageWidth - config.margins.right - lineWidth - 10;
  doc.line(clientX, lineY, clientX + lineWidth, lineY);
  doc.text('Client Signature', clientX, lineY + 5, textOptions);

  // Date lines
  const dateY = lineY + 15;
  const dateWidth = 40;
  
  const providerDateX = isRTL 
    ? pageWidth - config.margins.right - dateWidth
    : config.margins.left + 10;
  doc.line(providerDateX, dateY, providerDateX + dateWidth, dateY);
  doc.text('Date', providerDateX, dateY + 5, textOptions);

  const clientDateX = isRTL 
    ? config.margins.left + 10
    : pageWidth - config.margins.right - dateWidth - 10;
  doc.line(clientDateX, dateY, clientDateX + dateWidth, dateY);
  doc.text('Date', clientDateX, dateY + 5, textOptions);

  return currentY + 50;
};

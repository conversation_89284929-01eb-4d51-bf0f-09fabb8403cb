import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from '../Sidebar/Sidebar';
import VerticalIconNav from '../VerticalIconNav/VerticalIconNav';
// MODIFIED: Added FiBriefcase, FiSave, and FiStar for business links
import { FiFileText, FiShare2, FiArchive, FiBriefcase, FiSave, FiStar, FiHelpCircle } from 'react-icons/fi';
import { ChatBubbleLeftEllipsisIcon as ChatIconOutlineHero } from '@heroicons/react/24/outline';

const MD_BREAKPOINT = 768;
const LG_BREAKPOINT = 1024;

const pdfNavLinks = [ 
  { to: '/app/pdf/summary', text: 'Summarize PDF', icon: <FiFileText className="w-5 h-5" />, requiresAuth: false, description: 'Generate AI summaries' },
  { to: '/app/pdf/chat', text: 'Chat with PDF', icon: <ChatIconOutlineHero className="w-5 h-5" />, requiresAuth: false, description: 'Chat with documents' },
  { to: '/app/pdf/mindmap', text: 'PDF to Mind Map', icon: <FiShare2 className="w-5 h-5" />, requiresAuth: false, description: 'Visualize structure' },
  { to: '/app/pdf/saved', text: 'Saved PDF Items', icon: <FiArchive className="w-5 h-5" />, requiresAuth: true, description: 'Your saved analyses' },
];

// MODIFIED: Added Investor Pitch Generator, Business Q&A, and Legal Contract Generator links
const businessNavLinks = [
  { to: '/app/business/create', text: 'Generate Business Plan', icon: <FiBriefcase className="w-5 h-5" />, requiresAuth: true, description: 'Create a new business plan' },
  { to: '/app/business/pitch-generator', text: 'Investor Pitch Generator', icon: <FiStar className="w-5 h-5" />, requiresAuth: true, description: 'Create a compelling investor pitch' },
  { to: '/app/business/qa', text: 'Business Q&A', icon: <FiHelpCircle className="w-5 h-5" />, requiresAuth: true, description: 'Get expert business advice' },
  { to: '/app/business/contract', text: 'Legal Contract Generator', icon: <FiFileText className="w-5 h-5" />, requiresAuth: true, description: 'Generate professional service contracts' },
  { to: '/app/business/saved', text: 'Saved Plans', icon: <FiSave className="w-5 h-5" />, requiresAuth: true, description: 'Access your saved plans' },
];

const LayoutManager = ({
  children,
  openLoginModal,
  openAuthPromptModal,
  showSidebar = true,
  openComingSoonModal,
}) => {
  const location = useLocation();

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  const isMediumScreen = useMemo(() => windowWidth >= MD_BREAKPOINT && windowWidth < LG_BREAKPOINT, [windowWidth]);
  const isLargeScreen = useMemo(() => windowWidth >= LG_BREAKPOINT, [windowWidth]);

  const [internalSidebarOpenState, setInternalSidebarOpenState] = useState(
    showSidebar && windowWidth >= MD_BREAKPOINT
  );

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []); 

  useEffect(() => {
    if (showSidebar) {
      setInternalSidebarOpenState(windowWidth >= MD_BREAKPOINT);
    } else {
      setInternalSidebarOpenState(false);
    }
  }, [windowWidth, showSidebar]);

  const isInBusinessSection = useMemo(() =>
    location.pathname.startsWith('/app/business'),
    [location.pathname]
  );
  
  const currentDetailedNavLinks = useMemo(() => {
    if (isInBusinessSection) {
      return businessNavLinks;
    }
    return pdfNavLinks;
  }, [isInBusinessSection]);


  const toggleMainSidebar = useCallback(() => {
    if (showSidebar && (isMediumScreen || isLargeScreen)) {
      setInternalSidebarOpenState(prev => !prev);
    }
  }, [showSidebar, isMediumScreen, isLargeScreen]);

  const shouldShowVerticalIconNav = useMemo(() =>
    showSidebar &&
    !['/verify-email', '/main', '/pricing'].includes(location.pathname) &&
    !location.pathname.startsWith('/mindmap/display'),
    [location.pathname, showSidebar]
  );

  const shouldShowLayoutManagerDecorativeBg = useMemo(() =>
    location.pathname !== '/pricing',
    [location.pathname]
  );

  const shouldRenderDetailedSidebar = useMemo(() =>
    showSidebar && (isMediumScreen || isLargeScreen),
    [showSidebar, isMediumScreen, isLargeScreen]
  );

  const childrenProps = useMemo(() => ({
    openAuthPromptModal,
    openLoginModal,
    openComingSoonModal
  }), [openAuthPromptModal, openLoginModal, openComingSoonModal]);

  return (
    <div className="h-screen flex flex-col md:flex-row text-slate-100 overflow-hidden">
      {shouldShowLayoutManagerDecorativeBg && (
        <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
          <div className='absolute top-0 right-0 w-[400px] h-[220px] bg-[#03f] blur-[250px]'/>
          <div className='absolute top-[50%] left-0 w-[400px] h-[420px] bg-[#1ffff871] blur-[300px]'/>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-[70rem] h-[70rem] sm:w-[80rem] sm:h-[80rem] md:w-[90rem] md:h-[90rem] bg-sky-600/5 rounded-full blur-3xl"/>
        </div>
      )}

      {shouldShowVerticalIconNav && (
        <VerticalIconNav
          openComingSoonModal={openComingSoonModal}
          sidebarNavLinks={currentDetailedNavLinks}
        />
      )}

      <div className="relative z-10 flex flex-grow h-full overflow-hidden">
        {shouldRenderDetailedSidebar && (
          <Sidebar
            isOpen={internalSidebarOpenState}
            toggleSidebar={toggleMainSidebar}
            onAuthActionClick={openLoginModal}
            navLinks={currentDetailedNavLinks}
            isInBusinessSection={isInBusinessSection}
          />
        )}

        <main
          className={`
            flex-grow lg:bg-transparent h-full overflow-y-auto nice-scrollbar
            transition-all duration-300 ease-in-out
            relative z-10
          `}
        >
          <div className="flex-grow flex flex-col md:p-4 min-h-full w-full">
            {React.Children.map(children, child =>
              React.isValidElement(child)
                ? React.cloneElement(child, childrenProps)
                : child
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default React.memo(LayoutManager);
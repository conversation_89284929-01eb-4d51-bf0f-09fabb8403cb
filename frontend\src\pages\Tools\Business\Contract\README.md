# Service Contract Generator

A comprehensive AI-powered tool for generating professional service contracts with a multi-step wizard interface.

## Features

### 🎯 Multi-Step Wizard
- **Step 1**: Parties and Contract Details
- **Step 2**: Work Scope Definition  
- **Step 3**: Timeline and Payment Conditions
- **Step 4**: Optional Clauses
- **Step 5**: Contract Result with AI Generation

### 🎨 Professional UI/UX
- Dark theme with colored overlays (blue, green, purple, orange, red)
- Responsive design for all screen sizes
- Interactive tooltips and examples for all form fields
- Progress bar and step indicators
- Professional typography hierarchy

### 🤖 AI-Powered Generation
- Uses Gemini AI for contract generation
- Detailed prompts for comprehensive contracts
- Support for multiple languages (English, Arabic, French, etc.)
- Multiple jurisdictions (Algeria, USA, UK, etc.)

### 📋 Contract Features
- Comprehensive parties information
- Detailed work scope and deliverables
- Timeline and payment terms
- Optional protective clauses:
  - Death/Disability clause
  - Non-compete clause
  - Executive summary
  - Custom terms and conditions

### 🌐 Internationalization
- Multi-language support
- RTL (Right-to-Left) text direction for Arabic and other RTL languages
- Automatic text direction detection

### 📄 Professional Formatting
- Uses established tag system (~H~, ~S~, ~P~, etc.)
- Color-coded headers and sections
- Professional contract layout
- Signature blocks and legal formatting

## File Structure

```
Contract/
├── LandingPage.jsx                 # Main landing page with contract card
└── servicesContract/
    ├── ServiceContractGenerator.jsx # Main wizard component
    ├── data/
    │   ├── stepData.js             # Step configuration
    │   └── fieldLabels.js          # Field labels, icons, tooltips
    ├── form/
    │   ├── FormInput.jsx           # Input component with tooltips
    │   ├── FormTextArea.jsx        # Textarea component
    │   ├── FormSelect.jsx          # Select dropdown component
    │   └── FormCheckbox.jsx        # Checkbox component
    ├── steps/
    │   ├── Step1PartiesDetails.jsx # Parties information
    │   ├── Step2WorkScope.jsx      # Work scope definition
    │   ├── Step3TimelinePayment.jsx # Timeline and payment
    │   ├── Step4OptionalClauses.jsx # Optional clauses
    │   └── Step5ContractResult.jsx  # Result display
    └── format/
        ├── contractContentFormatter.js    # Content parsing logic
        ├── FormattedContractDisplay.jsx   # Contract display component
        └── contract-rtl-styles.css        # RTL styling support
```

## Usage

1. Navigate to `/app/business/contract` to see the landing page
2. Click "Create Service Contract" to start the wizard
3. Fill out the 5-step form with contract details
4. Review and download the generated contract

## Backend Integration

- Endpoint: `POST /api/legal-contracts/generate`
- Uses existing Gemini AI service
- Enhanced prompts for legal contract generation
- Supports all form data from the wizard

## Styling

- Consistent with existing business tools design
- Dark theme with gradient overlays
- Professional color scheme:
  - Blue: Primary actions and headers
  - Green: Success states and deliverables
  - Purple: Timeline and phases
  - Orange/Red: Optional clauses and warnings
  - Cyan: Additional information

## Legal Disclaimer

All generated contracts include appropriate legal disclaimers and recommendations for professional legal review.

import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>rrowRight, FiArrowLeft } from 'react-icons/fi';
import FormInput from '../form/FormInput';
import FormTextArea from '../form/FormTextArea';
import { fieldLabels } from '../data/fieldLabels';

const Step3TimelinePayment = ({ formData, onChange, onNext, onBack, isValid }) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full mb-4 backdrop-blur-sm border border-white/10">
          <FiClock className="w-6 h-6 text-purple-400 mr-2" />
          <span className="text-purple-400 font-semibold">Step 3 of 5</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-2">
          Timeline and Payment Conditions
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          Set project timeline, payment terms, and additional conditions
        </p>
      </div>

      {/* Form */}
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
        <div className="space-y-8">
          {/* Timeline Section */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
              Project Timeline
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <FormInput
                {...fieldLabels.projectPeriod}
                name="projectPeriod"
                value={formData.projectPeriod || ''}
                onChange={handleInputChange}
              />
            </div>
            <div className="mt-6">
              <FormTextArea
                {...fieldLabels.projectPhases}
                name="projectPhases"
                value={formData.projectPhases || ''}
                onChange={handleInputChange}
                rows={4}
              />
            </div>
          </div>

          {/* Payment Section */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
              Payment Terms
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <FormInput
                {...fieldLabels.totalAmount}
                name="totalAmount"
                value={formData.totalAmount || ''}
                onChange={handleInputChange}
              />
              <FormInput
                {...fieldLabels.paymentMethods}
                name="paymentMethods"
                value={formData.paymentMethods || ''}
                onChange={handleInputChange}
              />
            </div>
            <div className="mt-6">
              <FormTextArea
                {...fieldLabels.paymentSchedule}
                name="paymentSchedule"
                value={formData.paymentSchedule || ''}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
          </div>

          {/* Additional Terms */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-cyan-400 rounded-full mr-3"></div>
              Additional Conditions
              <span className="text-sm text-slate-400 ml-2">(Optional)</span>
            </h3>
            <FormTextArea
              {...fieldLabels.additionalTerms}
              name="additionalTerms"
              value={formData.additionalTerms || ''}
              onChange={handleInputChange}
              rows={4}
            />
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8 pt-6 border-t border-slate-700">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg font-semibold transition-all duration-200"
          >
            <FiArrowLeft className="w-4 h-4" />
            <span>Back to Work Scope</span>
          </button>
          
          <button
            onClick={onNext}
            disabled={!isValid}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
              isValid
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white shadow-lg hover:shadow-purple-500/25'
                : 'bg-slate-700 text-slate-400 cursor-not-allowed'
            }`}
          >
            <span>Continue to Optional Clauses</span>
            <FiArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step3TimelinePayment;

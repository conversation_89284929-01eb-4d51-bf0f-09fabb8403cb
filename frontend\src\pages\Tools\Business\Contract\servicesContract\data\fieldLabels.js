import { 
  FiUser, 
  FiMail, 
  FiMapPin, 
  FiCalendar, 
  FiGlobe, 
  FiFileText, 
  FiPackage, 
  FiXCircle, 
  FiClock, 
  FiLayers, 
  FiDollarSign, 
  FiCreditCard, 
  FiSettings, 
  FiShield, 
  FiEdit3 
} from 'react-icons/fi';

export const fieldLabels = {
  // Step 1: Parties and Contract Details
  providerName: {
    label: "Service Provider Name",
    icon: FiUser,
    placeholder: "Enter your company or personal name",
    example: "e.g., TechSolutions LLC or John Smith",
    tooltip: "The legal name of the person or company providing the services",
    required: true
  },
  providerEmail: {
    label: "Provider Email Address",
    icon: FiMail,
    placeholder: "<EMAIL>",
    example: "e.g., <EMAIL>",
    tooltip: "Primary email address for contract communications",
    required: true
  },
  providerAddress: {
    label: "Provider Address",
    icon: FiMapPin,
    placeholder: "Enter complete business address",
    example: "e.g., 123 Business St, City, State, ZIP, Country",
    tooltip: "Full legal address of the service provider",
    required: true
  },
  clientName: {
    label: "Client Name",
    icon: FiUser,
    placeholder: "Enter client's name or company",
    example: "e.g., ABC Corporation or Jane Doe",
    tooltip: "The legal name of the person or company receiving the services",
    required: true
  },
  clientEmail: {
    label: "Client Email Address",
    icon: FiMail,
    placeholder: "<EMAIL>",
    example: "e.g., <EMAIL>",
    tooltip: "Primary email address for client communications",
    required: true
  },
  clientAddress: {
    label: "Client Address",
    icon: FiMapPin,
    placeholder: "Enter client's complete address",
    example: "e.g., 456 Client Ave, City, State, ZIP, Country",
    tooltip: "Full legal address of the client",
    required: true
  },
  effectiveDate: {
    label: "Contract Effective Date",
    icon: FiCalendar,
    placeholder: "Select start date",
    example: "e.g., January 15, 2024",
    tooltip: "The date when this contract becomes legally effective",
    required: true
  },
  jurisdiction: {
    label: "Governing Jurisdiction",
    icon: FiGlobe,
    placeholder: "Select country/state",
    example: "e.g., Algeria, United States, United Kingdom",
    tooltip: "The legal jurisdiction that will govern this contract",
    required: true
  },
  contractLanguage: {
    label: "Contract Language",
    icon: FiGlobe,
    placeholder: "Select language",
    example: "e.g., English, Arabic, French",
    tooltip: "The language in which the contract will be generated",
    required: true
  },

  // Step 2: Work Scope
  serviceDescription: {
    label: "Description of Services",
    icon: FiFileText,
    placeholder: "Describe the services to be provided in detail",
    example: "e.g., Web development including frontend design, backend development, database setup, and API integration",
    tooltip: "Detailed description of all services to be provided under this contract",
    required: true
  },
  deliverables: {
    label: "Key Deliverables",
    icon: FiPackage,
    placeholder: "List specific deliverables",
    example: "e.g., Source Code, Design Files, Documentation, Training Materials",
    tooltip: "Specific items or outcomes that will be delivered to the client",
    required: true
  },
  exclusions: {
    label: "Service Exclusions",
    icon: FiXCircle,
    placeholder: "What is NOT included in the services",
    example: "e.g., Ongoing Maintenance, Content Creation, Third-party Licenses",
    tooltip: "Services or items that are explicitly not included in this contract",
    required: false
  },

  // Step 3: Timeline and Payment
  projectPeriod: {
    label: "Project Duration",
    icon: FiClock,
    placeholder: "Enter project timeline",
    example: "e.g., 3 weeks, 2 months, 6 weeks",
    tooltip: "Total time expected to complete the project",
    required: true
  },
  projectPhases: {
    label: "Project Phases",
    icon: FiLayers,
    placeholder: "Describe project phases and milestones",
    example: "e.g., Phase 1: Design (Week 1-2), Phase 2: Development (Week 3-5), Phase 3: Testing & Deployment (Week 6)",
    tooltip: "Breakdown of project into phases with timelines",
    required: true
  },
  totalAmount: {
    label: "Total Contract Amount",
    icon: FiDollarSign,
    placeholder: "Enter total fee",
    example: "e.g., $5,000, €3,500, 500,000 DZD",
    tooltip: "Total compensation for all services under this contract",
    required: true
  },
  paymentSchedule: {
    label: "Payment Schedule",
    icon: FiCreditCard,
    placeholder: "Describe payment terms",
    example: "e.g., 50% upfront, 25% at midpoint, 25% on completion",
    tooltip: "When and how payments will be made",
    required: true
  },
  paymentMethods: {
    label: "Accepted Payment Methods",
    icon: FiCreditCard,
    placeholder: "List accepted payment methods",
    example: "e.g., Bank Transfer, PayPal, Credit Card, Check",
    tooltip: "Payment methods that will be accepted",
    required: true
  },
  additionalTerms: {
    label: "Additional Financial Terms",
    icon: FiSettings,
    placeholder: "Any additional terms or conditions",
    example: "e.g., Overtime rate: $75/hour, Late payment fee: 2% per month, Termination notice: 14 days",
    tooltip: "Additional terms like overtime rates, late fees, termination conditions",
    required: false
  },

  // Step 4: Optional Clauses
  includeDeathClause: {
    label: "Include Death/Disability Clause",
    icon: FiShield,
    tooltip: "Adds provisions for contract handling in case of death or disability of service provider",
    required: false
  },
  includeNonCompete: {
    label: "Include Non-Compete Clause",
    icon: FiShield,
    tooltip: "Adds restrictions on working with competitors during and after the contract period",
    required: false
  },
  includeExecutiveSummary: {
    label: "Include Executive Summary",
    icon: FiFileText,
    tooltip: "Adds a brief overview section at the beginning of the contract",
    required: false
  },
  customClauses: {
    label: "Custom Terms & Conditions",
    icon: FiEdit3,
    placeholder: "Enter any custom clauses or special terms",
    example: "e.g., Intellectual property rights, confidentiality requirements, specific performance standards",
    tooltip: "Any additional custom terms specific to your business relationship",
    required: false
  }
};

export const jurisdictionOptions = [
  "Algeria",
  "United States",
  "United Kingdom", 
  "Canada",
  "Australia",
  "France",
  "Germany",
  "Spain",
  "Italy",
  "Netherlands",
  "Other"
];

export const languageOptions = [
  "English",
  "Arabic", 
  "French",
  "Spanish",
  "German",
  "Italian",
  "Portuguese",
  "Other"
];

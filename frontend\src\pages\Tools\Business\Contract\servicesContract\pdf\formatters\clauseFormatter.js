// PDF Clause Formatter
// Specialized formatting for contract clauses and terms

/**
 * Render contract clause with highlighted background
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Clause content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractClause = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 15;

  // Set clause font
  doc.setFont(config.fonts.bold);
  doc.setFontSize(11);
  doc.setTextColor(config.colors.text);

  // Calculate dimensions
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 6;
  const totalHeight = lines.length * lineHeight + 10;

  // Add highlighted background (matching web display)
  doc.setFillColor(255, 251, 235); // Light yellow
  doc.rect(config.margins.left, currentY - 5, maxWidth + 15, totalHeight, 'F');

  // Add colored left border (matching web display)
  doc.setFillColor(245, 158, 11); // Yellow/orange
  const borderX = isRTL 
    ? pageWidth - config.margins.right - 5
    : config.margins.left;
  doc.rect(borderX, currentY - 5, 5, totalHeight, 'F');

  // Calculate text position
  const textX = isRTL 
    ? pageWidth - config.margins.right - 8
    : config.margins.left + 10;

  // Render text
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, textX, currentY, textOptions);

  return currentY + totalHeight + 8;
};

/**
 * Render contract term with blue styling
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Term content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractTerm = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 15;

  // Set term font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(11);
  doc.setTextColor(config.colors.primary);

  // Calculate dimensions
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 6;
  const totalHeight = lines.length * lineHeight + 8;

  // Add blue background (matching web display)
  doc.setFillColor(239, 246, 255); // Light blue
  doc.rect(config.margins.left, currentY - 4, maxWidth + 15, totalHeight, 'F');

  // Add blue border
  doc.setDrawColor(config.colors.primary);
  doc.setLineWidth(1);
  doc.rect(config.margins.left, currentY - 4, maxWidth + 15, totalHeight);

  // Calculate text position
  const textX = isRTL 
    ? pageWidth - config.margins.right - 8
    : config.margins.left + 8;

  // Render text
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, textX, currentY, textOptions);

  return currentY + totalHeight + 6;
};

/**
 * Render signature block with professional styling
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Signature content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderSignatureBlock = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const blockWidth = pageWidth - config.margins.left - config.margins.right;

  // Set signature font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(11);
  doc.setTextColor(config.colors.text);

  // Add dashed border (matching web display)
  doc.setDrawColor(config.colors.light);
  doc.setLineWidth(1);
  doc.setLineDashPattern([3, 3], 0);
  doc.rect(config.margins.left, currentY - 8, blockWidth, 70);
  doc.setLineDashPattern([], 0); // Reset dash pattern

  // Render content centered
  const textX = isRTL
    ? pageWidth - config.margins.right - 8
    : config.margins.left + blockWidth / 2;
  const textOptions = isRTL ? { align: 'right' } : { align: 'center' };
  doc.text(content, textX, currentY, textOptions);

  // Service Provider Section
  const leftSectionX = config.margins.left + 15;
  const rightSectionX = pageWidth - config.margins.right - 100;

  // Service Provider Signature
  let signatureY = currentY + 20;
  doc.setDrawColor(config.colors.text);
  doc.setLineWidth(0.8);
  doc.line(leftSectionX, signatureY, leftSectionX + 80, signatureY);

  doc.setFontSize(9);
  doc.setTextColor(config.colors.light);
  doc.text('Signature', leftSectionX, signatureY - 2);

  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);
  doc.text('**Service Provider:**', leftSectionX, signatureY + 8);

  // Service Provider Date
  let dateY = signatureY + 20;
  doc.setLineWidth(0.8);
  doc.line(leftSectionX, dateY, leftSectionX + 60, dateY);

  doc.setFontSize(9);
  doc.setTextColor(config.colors.light);
  doc.text('Date', leftSectionX, dateY - 2);

  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);
  doc.text('Date:', leftSectionX, dateY + 8);

  // Client Signature
  doc.setDrawColor(config.colors.text);
  doc.setLineWidth(0.8);
  doc.line(rightSectionX, signatureY, rightSectionX + 80, signatureY);

  doc.setFontSize(9);
  doc.setTextColor(config.colors.light);
  doc.text('Signature', rightSectionX, signatureY - 2);

  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);
  doc.text('**Client:**', rightSectionX, signatureY + 8);

  // Client Date
  doc.setLineWidth(0.8);
  doc.line(rightSectionX, dateY, rightSectionX + 60, dateY);

  doc.setFontSize(9);
  doc.setTextColor(config.colors.light);
  doc.text('Date', rightSectionX, dateY - 2);

  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);
  doc.text('Date:', rightSectionX, dateY + 8);

  return currentY + 75;
};

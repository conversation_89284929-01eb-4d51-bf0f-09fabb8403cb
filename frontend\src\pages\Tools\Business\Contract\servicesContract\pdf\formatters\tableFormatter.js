// PDF Table Formatter
// Specialized formatting for contract tables (parties information)

/**
 * Render contract table with professional styling
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Table content (pipe-separated)
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractTable = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const tableWidth = pageWidth - config.margins.left - config.margins.right;

  // Parse table content - filter out empty rows and ensure they contain pipes
  const rows = content.split('\n').filter(row => row.trim() && row.includes('|'));
  if (rows.length === 0) return currentY;

  // Calculate column widths (assuming 3 columns: Field, Service Provider, Client)
  const colWidths = [tableWidth * 0.25, tableWidth * 0.375, tableWidth * 0.375];
  
  let yPosition = currentY;
  const rowHeight = 12;

  // Add table border background
  const totalTableHeight = rows.length * rowHeight + 4;
  doc.setFillColor(248, 250, 252); // Light background
  doc.setDrawColor(config.colors.light);
  doc.setLineWidth(0.5);
  doc.rect(config.margins.left, yPosition - 2, tableWidth, totalTableHeight, 'FD');

  // Render header row first
  if (rows.length > 0) {
    const headerCells = ['Field', 'Service Provider', 'Client'];

    // Header background
    doc.setFillColor(30, 41, 59); // Dark slate
    doc.rect(config.margins.left, yPosition - 2, tableWidth, rowHeight, 'F');

    headerCells.forEach((header, cellIndex) => {
      const cellX = config.margins.left + colWidths.slice(0, cellIndex).reduce((a, b) => a + b, 0);
      const textX = cellX + 5;

      doc.setFont(config.fonts.bold);
      doc.setFontSize(10);
      doc.setTextColor(99, 179, 237); // Blue color

      doc.text(header, textX, yPosition + 6);

      // Add cell borders
      doc.setDrawColor(config.colors.light);
      doc.setLineWidth(0.5);
      doc.rect(cellX, yPosition - 2, colWidths[cellIndex], rowHeight);
    });

    yPosition += rowHeight;
  }

  // Render data rows (skip header row if it exists)
  const dataRows = rows.slice(1);
  dataRows.forEach((row, rowIndex) => {
    const cells = row.split('|').map(cell => cell.trim());

    // Row background for alternating colors
    if (rowIndex % 2 === 0) {
      doc.setFillColor(248, 250, 252); // Very light gray
      doc.rect(config.margins.left, yPosition - 2, tableWidth, rowHeight, 'F');
    }

    // Render each cell
    cells.forEach((cell, cellIndex) => {
      if (cellIndex >= colWidths.length) return; // Skip extra columns

      const cellX = config.margins.left + colWidths.slice(0, cellIndex).reduce((a, b) => a + b, 0);
      const textX = cellX + 5;

      // Set font based on cell type
      if (cellIndex === 0) {
        // First column (labels) - bold and colored
        doc.setFont(config.fonts.bold);
        doc.setFontSize(10);
        doc.setTextColor(config.colors.secondary);

        // Add colored background for label column
        doc.setFillColor(240, 245, 255); // Light blue
        doc.rect(cellX, yPosition - 2, colWidths[cellIndex], rowHeight, 'F');
      } else {
        // Data columns - regular font
        doc.setFont(config.fonts.regular);
        doc.setFontSize(10);
        doc.setTextColor(config.colors.text);
      }

      // Render cell text
      const maxCellWidth = colWidths[cellIndex] - 10; // Account for padding
      const cellText = doc.splitTextToSize(cell, maxCellWidth);

      doc.text(cellText[0] || '', textX, yPosition + 6);

      // Add cell borders
      doc.setDrawColor(config.colors.light);
      doc.setLineWidth(0.3);
      doc.rect(cellX, yPosition - 2, colWidths[cellIndex], rowHeight);
    });

    yPosition += rowHeight;
  });

  return yPosition + 10; // Extra spacing after table
};

/**
 * Create parties information table from form data
 * @param {Object} formData - Form data containing parties information
 * @returns {string} - Formatted table content
 */
export const createPartiesTable = (formData) => {
  const tableRows = [
    // Header row
    'Field|Service Provider|Client',
    // Data rows
    `Name|${formData.providerName || 'N/A'}|${formData.clientName || 'N/A'}`,
    `Email|${formData.providerEmail || 'N/A'}|${formData.clientEmail || 'N/A'}`,
    `Address|${formData.providerAddress || 'N/A'}|${formData.clientAddress || 'N/A'}`
  ];
  
  return tableRows.join('\n');
};

/**
 * Render parties information as a professional table
 * @param {jsPDF} doc - PDF document
 * @param {Object} formData - Form data
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderPartiesTable = (doc, formData, currentY, config) => {
  const tableContent = createPartiesTable(formData);
  return renderContractTable(doc, tableContent, currentY, config);
};

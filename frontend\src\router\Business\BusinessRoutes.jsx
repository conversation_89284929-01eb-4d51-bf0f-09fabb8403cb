import React from 'react';
import { Routes, Route } from 'react-router-dom';
import LayoutManager from '../../common/Layout/LayoutManager';
import AnimatedPageWrapper from '../../common/Animations/AnimatedPageWrapper';
import ProtectedRoute from '../../components/common/Auth/ProtectedRoute';

// Lazy load pages
const BusinessPlanPage = React.lazy(() => import('../../pages/Tools/Business/BusinessPlanPage'));
const SavedBusinessPlansPage = React.lazy(() => import('../../pages/Tools/Business/SavedBusinessPlansPage'));
// ADDED: Lazy load for the new Investor Pitch page
const InvestorPitchGeneratorPage = React.lazy(() => import('../../pages/Tools/Business/InvestorPitchGeneratorPage'));
// ADDED: Lazy load for the new Business Q&A page
const BusinessQAPage = React.lazy(() => import('../../pages/Tools/Business/BusinessQAPage'));
// ADDED: Lazy load for the new Legal Contract Generator page
const ContractLandingPage = React.lazy(() => import('../../pages/Tools/Business/Contract/LandingPage'));
const ServiceContractGenerator = React.lazy(() => import('../../pages/Tools/Business/Contract/servicesContract/ServiceContractGenerator'));


const BusinessRoutes = ({ openLoginModal, openAuthPromptModal, openComingSoonModal }) => {
  return (
    <LayoutManager
      showSidebar={true}
      openLoginModal={openLoginModal}
      openAuthPromptModal={openAuthPromptModal}
      openComingSoonModal={openComingSoonModal}
    >
      <Routes>
        <Route
          path="create"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <BusinessPlanPage />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
        <Route
          path="view/:planId"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <BusinessPlanPage />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
        <Route
          path="saved"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <SavedBusinessPlansPage />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
        {/* ADDED: Route for the Investor Pitch Generator */}
        <Route
          path="pitch-generator"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <InvestorPitchGeneratorPage />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
        {/* ADDED: Route for the Business Q&A */}
        <Route
          path="qa"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <BusinessQAPage />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
        {/* ADDED: Route for the Legal Contract Generator */}
        <Route
          path="contract"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <ContractLandingPage />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
        <Route
          path="contract/service"
          element={
            <ProtectedRoute>
              <AnimatedPageWrapper>
                <ServiceContractGenerator />
              </AnimatedPageWrapper>
            </ProtectedRoute>
          }
        />
  
      </Routes>
    </LayoutManager>
  );
};

export default BusinessRoutes;
// PDF Financial Terms Formatter
// Specialized formatting for financial terms with clean styling

/**
 * Render financial term with clean professional styling
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Financial term content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderFinancialTerm = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 15;

  // Parse the content to extract term name and description
  const parts = content.split(':');
  const termName = parts[0]?.trim();
  const termDescription = parts.slice(1).join(':').trim();

  // Calculate dimensions
  const nameLines = doc.splitTextToSize(termName, maxWidth);
  const descLines = doc.splitTextToSize(termDescription, maxWidth);
  const totalLines = nameLines.length + descLines.length;
  const lineHeight = 6;
  const totalHeight = totalLines * lineHeight + 12;

  // Add background (matching web display)
  doc.setFillColor(248, 250, 252); // Very light gray-blue
  doc.rect(config.margins.left, currentY - 5, maxWidth + 15, totalHeight, 'F');

  // Add left border (matching web display)
  doc.setFillColor(config.colors.primary); // Blue
  const borderX = isRTL 
    ? pageWidth - config.margins.right - 5
    : config.margins.left;
  doc.rect(borderX, currentY - 5, 4, totalHeight, 'F');

  // Calculate text position
  const textX = isRTL 
    ? pageWidth - config.margins.right - 8
    : config.margins.left + 10;

  let yPosition = currentY;

  // Render term name (header)
  doc.setFont(config.fonts.bold);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.secondary); // Cyan color
  
  const nameOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(termName.toUpperCase(), textX, yPosition, nameOptions);
  yPosition += nameLines.length * lineHeight + 3;

  // Render term description
  doc.setFont(config.fonts.regular);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.text);
  
  const descOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(termDescription, textX, yPosition, descOptions);

  return currentY + totalHeight + 6;
};

/**
 * Render multiple financial terms as a group
 * @param {jsPDF} doc - PDF document
 * @param {Array} terms - Array of financial term contents
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderFinancialTermsGroup = (doc, terms, currentY, config) => {
  let yPosition = currentY;
  
  // Add section header for financial terms
  doc.setFont(config.fonts.bold);
  doc.setFontSize(12);
  doc.setTextColor(config.colors.secondary);
  
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const headerX = isRTL 
    ? pageWidth - config.margins.right
    : config.margins.left;
  
  const headerOptions = isRTL ? { align: 'right' } : {};
  doc.text('Additional Financial Terms', headerX, yPosition, headerOptions);
  yPosition += 15;
  
  // Render each financial term
  terms.forEach((term, index) => {
    yPosition = renderFinancialTerm(doc, term, yPosition, config);
    
    // Add spacing between terms
    if (index < terms.length - 1) {
      yPosition += 3;
    }
  });
  
  return yPosition + 8; // Extra spacing after the group
};

/**
 * Parse financial terms from additional terms text
 * @param {string} additionalTerms - Raw additional terms text
 * @returns {Array} - Array of parsed financial terms
 */
export const parseFinancialTerms = (additionalTerms) => {
  if (!additionalTerms) return [];
  
  // Split by common financial term patterns
  const terms = [];
  const lines = additionalTerms.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    // Look for patterns like "Term Name: Description"
    if (line.includes(':') && (
      line.toLowerCase().includes('rate') ||
      line.toLowerCase().includes('fee') ||
      line.toLowerCase().includes('payment') ||
      line.toLowerCase().includes('cost') ||
      line.toLowerCase().includes('charge') ||
      line.toLowerCase().includes('notice') ||
      line.toLowerCase().includes('termination')
    )) {
      terms.push(line.trim());
    }
  });
  
  return terms;
};

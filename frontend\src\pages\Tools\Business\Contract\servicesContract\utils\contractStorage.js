// Contract local storage utilities
// Similar to business plan storage but for contracts

const CONTRACT_STORAGE_KEY = 'serviceContract';
const CONTRACT_EXPIRATION_HOURS = 1; // 1 hour expiration

/**
 * Save contract data to local storage with expiration
 * @param {Object} contractData - The contract data to save
 * @param {Object} formData - The form data used to generate the contract
 */
export const saveContractToStorage = (contractData, formData) => {
  try {
    const contractToSave = {
      contract: contractData,
      formData: formData,
      timestamp: Date.now(),
      expiresAt: Date.now() + (CONTRACT_EXPIRATION_HOURS * 60 * 60 * 1000)
    };
    
    localStorage.setItem(CONTRACT_STORAGE_KEY, JSON.stringify(contractToSave));
    console.log('[CONTRACT_STORAGE] Contract saved to local storage');
  } catch (error) {
    console.error('[CONTRACT_STORAGE] Error saving contract to storage:', error);
  }
};

/**
 * Load contract data from local storage
 * @returns {Object|null} - The contract data or null if not found/expired
 */
export const loadContractFromStorage = () => {
  try {
    const storedData = localStorage.getItem(CONTRACT_STORAGE_KEY);
    if (!storedData) {
      return null;
    }

    const parsedData = JSON.parse(storedData);
    const now = Date.now();

    // Check if contract has expired
    if (now > parsedData.expiresAt) {
      console.log('[CONTRACT_STORAGE] Contract expired, removing from storage');
      clearContractFromStorage();
      return null;
    }

    console.log('[CONTRACT_STORAGE] Contract loaded from local storage');
    return parsedData;
  } catch (error) {
    console.error('[CONTRACT_STORAGE] Error loading contract from storage:', error);
    clearContractFromStorage(); // Clear corrupted data
    return null;
  }
};

/**
 * Clear contract data from local storage
 */
export const clearContractFromStorage = () => {
  try {
    localStorage.removeItem(CONTRACT_STORAGE_KEY);
    console.log('[CONTRACT_STORAGE] Contract cleared from local storage');
  } catch (error) {
    console.error('[CONTRACT_STORAGE] Error clearing contract from storage:', error);
  }
};

/**
 * Check if there's a valid contract in storage
 * @returns {boolean} - True if valid contract exists
 */
export const hasValidContractInStorage = () => {
  const contractData = loadContractFromStorage();
  return contractData !== null;
};

/**
 * Get contract expiration time in minutes
 * @returns {number|null} - Minutes until expiration or null if no contract
 */
export const getContractExpirationMinutes = () => {
  try {
    const storedData = localStorage.getItem(CONTRACT_STORAGE_KEY);
    if (!storedData) {
      return null;
    }

    const parsedData = JSON.parse(storedData);
    const now = Date.now();
    const timeLeft = parsedData.expiresAt - now;

    if (timeLeft <= 0) {
      return 0;
    }

    return Math.ceil(timeLeft / (60 * 1000)); // Convert to minutes
  } catch (error) {
    console.error('[CONTRACT_STORAGE] Error getting expiration time:', error);
    return null;
  }
};

/**
 * Update contract content while preserving form data and timestamp
 * @param {string} newContractContent - The new contract content
 */
export const updateContractContent = (newContractContent) => {
  try {
    const existingData = loadContractFromStorage();
    if (!existingData) {
      console.warn('[CONTRACT_STORAGE] No existing contract to update');
      return;
    }

    const updatedData = {
      ...existingData,
      contract: newContractContent,
      timestamp: Date.now() // Update timestamp for the new content
    };

    localStorage.setItem(CONTRACT_STORAGE_KEY, JSON.stringify(updatedData));
    console.log('[CONTRACT_STORAGE] Contract content updated in storage');
  } catch (error) {
    console.error('[CONTRACT_STORAGE] Error updating contract content:', error);
  }
};

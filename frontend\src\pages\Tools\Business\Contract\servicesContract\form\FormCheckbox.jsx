import React, { useState } from 'react';
import { FiInfo, FiCheck } from 'react-icons/fi';

const FormCheckbox = ({ 
  label, 
  name, 
  checked, 
  onChange, 
  tooltip, 
  icon: Icon, 
  className = ""
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Custom Checkbox */}
          <div className="relative">
            <input
              type="checkbox"
              name={name}
              checked={checked}
              onChange={onChange}
              className="sr-only"
            />
            <div
              onClick={() => onChange({ target: { name, checked: !checked } })}
              className={`w-5 h-5 rounded border-2 cursor-pointer transition-all duration-200 flex items-center justify-center ${
                checked 
                  ? 'bg-blue-500 border-blue-500' 
                  : 'bg-slate-800/50 border-slate-600 hover:border-blue-400'
              }`}
            >
              {checked && <FiCheck className="w-3 h-3 text-white" />}
            </div>
          </div>

          {/* Label with Icon */}
          <label
            className="flex items-center space-x-2 text-sm font-medium text-slate-200 cursor-pointer"
            onClick={() => onChange({ target: { name, checked: !checked } })}
          >
            {Icon && <Icon className="w-4 h-4 text-blue-400" />}
            <span>{label}</span>
          </label>
        </div>
        
        {/* Tooltip */}
        {tooltip && (
          <div className="relative">
            <button
              type="button"
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              className="text-slate-400 hover:text-blue-400 transition-colors"
            >
              <FiInfo className="w-4 h-4" />
            </button>
            
            {showTooltip && (
              <div className="absolute right-0 top-6 z-10 w-64 p-3 bg-slate-800 border border-slate-600 rounded-lg shadow-xl">
                <p className="text-xs text-slate-300">{tooltip}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FormCheckbox;

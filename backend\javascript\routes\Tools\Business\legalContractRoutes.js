// src/routes/Tools/Business/legalContractRoutes.js
import express from 'express';
import { generateServiceContract } from '../../../controllers/Tools/BusinessPlan/legalContractController.js';
import { protect } from '../../../middleware/authMiddleware.js';

const router = express.Router();

// @route POST /api/legal-contracts/generate
router.post('/generate', protect, generateServiceContract);

export default router;
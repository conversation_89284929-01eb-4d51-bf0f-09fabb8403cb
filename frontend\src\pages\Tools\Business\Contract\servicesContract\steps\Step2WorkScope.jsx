import React from 'react';
import { FiFileText, FiArrowRight, FiArrowLeft } from 'react-icons/fi';
import FormTextArea from '../form/FormTextArea';
import { fieldLabels } from '../data/fieldLabels';

const Step2WorkScope = ({ formData, onChange, onNext, onBack, isValid }) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full mb-4 backdrop-blur-sm border border-white/10">
          <FiFileText className="w-6 h-6 text-green-400 mr-2" />
          <span className="text-green-400 font-semibold">Step 2 of 5</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-green-200 to-emerald-200 bg-clip-text text-transparent mb-2">
          Work Scope Definition
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          Define the services, deliverables, and exclusions for this contract
        </p>
      </div>

      {/* Form */}
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
        <div className="space-y-8">
          {/* Service Description */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Service Description
            </h3>
            <FormTextArea
              {...fieldLabels.serviceDescription}
              name="serviceDescription"
              value={formData.serviceDescription || ''}
              onChange={handleInputChange}
              rows={6}
            />
          </div>

          {/* Deliverables */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Key Deliverables
            </h3>
            <FormTextArea
              {...fieldLabels.deliverables}
              name="deliverables"
              value={formData.deliverables || ''}
              onChange={handleInputChange}
              rows={4}
            />
          </div>

          {/* Exclusions */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
              Service Exclusions
              <span className="text-sm text-slate-400 ml-2">(Optional)</span>
            </h3>
            <FormTextArea
              {...fieldLabels.exclusions}
              name="exclusions"
              value={formData.exclusions || ''}
              onChange={handleInputChange}
              rows={4}
            />
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8 pt-6 border-t border-slate-700">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg font-semibold transition-all duration-200"
          >
            <FiArrowLeft className="w-4 h-4" />
            <span>Back to Parties</span>
          </button>
          
          <button
            onClick={onNext}
            disabled={!isValid}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
              isValid
                ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white shadow-lg hover:shadow-green-500/25'
                : 'bg-slate-700 text-slate-400 cursor-not-allowed'
            }`}
          >
            <span>Continue to Timeline</span>
            <FiArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step2WorkScope;

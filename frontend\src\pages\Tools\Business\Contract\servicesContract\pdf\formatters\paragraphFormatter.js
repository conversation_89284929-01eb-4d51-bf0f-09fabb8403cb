// PDF Paragraph Formatter
// Specialized formatting for contract paragraphs with Poppins font styling

/**
 * Render contract paragraph with indentation and background
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Paragraph content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractParagraph = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right - 15; // Extra margin for indentation

  // Set paragraph font - Poppins-like styling
  doc.setFont(config.fonts.regular);
  doc.setFontSize(11);
  doc.setTextColor(config.colors.text);

  // Calculate position with indentation (matching web display)
  const xPosition = isRTL 
    ? pageWidth - config.margins.right - 8
    : config.margins.left + 8;

  // Split text to fit width
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 6;
  const totalHeight = lines.length * lineHeight + 6;

  // Add background highlight (matching web display)
  doc.setFillColor(248, 250, 252); // Very light gray
  const rectX = isRTL 
    ? config.margins.left
    : config.margins.left + 4;
  doc.rect(rectX, currentY - 4, maxWidth + 12, totalHeight, 'F');

  // Add left border (matching web display)
  doc.setDrawColor(config.colors.light);
  doc.setLineWidth(2);
  const borderX = isRTL 
    ? pageWidth - config.margins.right - 4
    : config.margins.left + 4;
  doc.line(borderX, currentY - 4, borderX, currentY + totalHeight - 4);

  // Render text with proper spacing
  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, xPosition, currentY, textOptions);

  return currentY + totalHeight + 8;
};

/**
 * Render plain text without special formatting
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Text content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderPlainText = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;
  const maxWidth = pageWidth - config.margins.left - config.margins.right;

  // Set plain text font
  doc.setFont(config.fonts.regular);
  doc.setFontSize(10);
  doc.setTextColor(config.colors.light);

  // Calculate position
  const xPosition = isRTL 
    ? pageWidth - config.margins.right
    : config.margins.left;

  // Split text and render
  const lines = doc.splitTextToSize(content, maxWidth);
  const lineHeight = 5;
  const totalHeight = lines.length * lineHeight;

  const textOptions = isRTL ? { align: 'right', maxWidth } : { maxWidth };
  doc.text(content, xPosition, currentY, textOptions);

  return currentY + totalHeight + 5;
};

import React from 'react';
import { FiShield, FiArrowRight, FiArrowLeft } from 'react-icons/fi';
import FormCheckbox from '../form/FormCheckbox';
import FormTextArea from '../form/FormTextArea';
import { fieldLabels } from '../data/fieldLabels';

const Step4OptionalClauses = ({ formData, onChange, onNext, onBack }) => {
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    onChange({ [name]: type === 'checkbox' ? checked : value });
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full mb-4 backdrop-blur-sm border border-white/10">
          <FiShield className="w-6 h-6 text-orange-400 mr-2" />
          <span className="text-orange-400 font-semibold">Step 4 of 5</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-orange-200 to-red-200 bg-clip-text text-transparent mb-2">
          Optional Clauses
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          Choose additional protective clauses and custom terms for your contract
        </p>
      </div>

      {/* Form */}
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
        <div className="space-y-8">
          {/* Standard Optional Clauses */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
              Standard Protective Clauses
            </h3>
            <div className="space-y-6">
              <div className="p-4 bg-slate-800/30 border border-slate-700/50 rounded-lg">
                <FormCheckbox
                  {...fieldLabels.includeDeathClause}
                  name="includeDeathClause"
                  checked={formData.includeDeathClause || false}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="p-4 bg-slate-800/30 border border-slate-700/50 rounded-lg">
                <FormCheckbox
                  {...fieldLabels.includeNonCompete}
                  name="includeNonCompete"
                  checked={formData.includeNonCompete || false}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="p-4 bg-slate-800/30 border border-slate-700/50 rounded-lg">
                <FormCheckbox
                  {...fieldLabels.includeExecutiveSummary}
                  name="includeExecutiveSummary"
                  checked={formData.includeExecutiveSummary || false}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          {/* Custom Terms */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-cyan-400 rounded-full mr-3"></div>
              Custom Terms & Conditions
            </h3>
            <FormTextArea
              {...fieldLabels.customClauses}
              name="customClauses"
              value={formData.customClauses || ''}
              onChange={handleInputChange}
              rows={6}
            />
          </div>

          {/* Information Box */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <FiShield className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-blue-400 font-semibold mb-2">Legal Notice</h4>
                <p className="text-slate-300 text-sm leading-relaxed">
                  These optional clauses provide additional protection and clarity to your contract. 
                  While our AI generates legally structured content, we recommend having any contract 
                  reviewed by a qualified legal professional before execution.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8 pt-6 border-t border-slate-700">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg font-semibold transition-all duration-200"
          >
            <FiArrowLeft className="w-4 h-4" />
            <span>Back to Timeline</span>
          </button>
          
          <button
            onClick={onNext}
            className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-500 hover:to-red-500 text-white rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-orange-500/25"
          >
            <span>Generate Contract</span>
            <FiArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step4OptionalClauses;

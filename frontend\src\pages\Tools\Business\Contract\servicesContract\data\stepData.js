// Step data configuration for Service Contract Generator

export const stepConfig = {
  1: {
    title: "Parties and Contract Details",
    description: "Enter information about service provider, client, and basic contract details",
    icon: "FiUsers",
    fields: [
      "providerName",
      "providerEmail", 
      "providerAddress",
      "clientName",
      "clientEmail",
      "clientAddress",
      "effectiveDate",
      "jurisdiction",
      "contractLanguage"
    ]
  },
  2: {
    title: "Work Scope",
    description: "Define the services, deliverables, and exclusions for this contract",
    icon: "FiFileText",
    fields: [
      "serviceDescription",
      "deliverables",
      "exclusions"
    ]
  },
  3: {
    title: "Timeline and Payment Conditions",
    description: "Set project timeline, payment terms, and additional conditions",
    icon: "FiClock",
    fields: [
      "projectPeriod",
      "projectPhases",
      "totalAmount",
      "paymentSchedule",
      "paymentMethods",
      "additionalTerms"
    ]
  },
  4: {
    title: "Optional Clauses",
    description: "Choose additional protective clauses and custom terms",
    icon: "FiShield",
    fields: [
      "includeDeathClause",
      "includeNonCompete",
      "includeExecutiveSummary",
      "customClauses"
    ]
  },
  5: {
    title: "Contract Result",
    description: "Review and download your AI-generated service contract",
    icon: "FiDownload",
    fields: []
  }
};

export const totalSteps = Object.keys(stepConfig).length;

import React from 'react';
import { parseContractContent, getTextDirection } from './contractContentFormatter';
import './contract-rtl-styles.css';

// Contract Header Component
const ContractHeader = ({ content }) => (
  <div className="text-center mb-8">
    <h1 className="text-4xl font-bold text-blue-400 mb-4 border-b-4 border-blue-400 pb-3 inline-block decoration-4">
      {content}
    </h1>
  </div>
);

// Section Header Component
const SectionHeader = ({ content }) => (
  <div className="mt-8 mb-6">
    <h2 className="section-header text-2xl font-bold text-cyan-400 mb-3 flex items-center border-b-2 border-cyan-400 pb-2  decoration-2">
      <div className="divider w-1 h-8 bg-cyan-400 mr-3 rounded"></div>
      {content}
    </h2>
  </div>
);

// Sub-section Header Component
const SubSectionHeader = ({ content }) => (
  <div className="mt-6 mb-4">
    <h3 className="text-xl font-semibold text-slate-200 mb-2 flex items-center">
      <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
      {content}
    </h3>
  </div>
);

// Paragraph Component
const ContractParagraph = ({ content }) => (
  <p className="text-gray-200 leading-relaxed mb-4 text-justify pl-4 ml-2 border-l-2 border-slate-600 bg-slate-800/20 p-3 rounded-r-lg font-['Poppins']">
    {content}
  </p>
);

// List Item Component
const ContractListItem = ({ content }) => (
  <div className="contract-list-item flex items-start mb-3 ml-6 pl-4">
    <div className="bullet w-2 h-2 bg-green-400 rounded-full mt-2 mr-4 flex-shrink-0"></div>
    <span className="text-white leading-relaxed  rounded flex-1">{content}</span>
  </div>
);

// Contract Clause Component
const ContractClause = ({ content }) => (
  <div className="contract-clause ml-6 bg-slate-800/30 border-l-4 border-yellow-400 p-4 mb-4 rounded-r-lg">
    <p className="text-slate-200 font-medium leading-relaxed">{content}</p>
  </div>
);

// Financial Terms Component (cleaner styling for financial terms)
const FinancialTerm = ({ content }) => {
  // Parse the content to extract term name and description
  const parts = content.split(':');
  const termName = parts[0]?.trim();
  const termDescription = parts.slice(1).join(':').trim();

  return (
    <div className="financial-term bg-slate-900/40 border-l-4 border-blue-400 p-4 mb-3 rounded-r-lg">
      <div className="flex flex-col space-y-2">
        <h4 className="text-blue-300 font-semibold text-sm uppercase tracking-wide">
          {termName}
        </h4>
        <p className="text-slate-300 leading-relaxed font-['Poppins']">
          {termDescription}
        </p>
      </div>
    </div>
  );
};

// Contract Term Component
const ContractTerm = ({ content }) => (
  <div className="bg-blue-900/20 border border-blue-500/30 p-4 mb-4 rounded-lg">
    <p className="text-white leading-relaxed">{content}</p>
  </div>
);

// Signature Block Component
const SignatureBlock = ({ content }) => {
  // Parse content to extract names if available
  const lines = content.split('\n').filter(line => line.trim());
  const mainContent = lines[0] || content;

  return (
    <div className="signature-block bg-slate-900/50 border-2 border-dashed border-slate-600 p-8 mb-6 rounded-lg">
      <p className="text-slate-300 text-center font-medium mb-8">{mainContent}</p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        {/* Service Provider Section */}
        <div className="signature-section space-y-6">
          <div>
            <div className="border-b-2 border-slate-500 pb-1 mb-3 h-12 relative">
              <span className="absolute bottom-0 left-0 text-slate-500 text-xs">Signature</span>
            </div>
            <p className="text-slate-300 text-sm font-semibold">**Service Provider:**</p>
          </div>

          <div>
            <div className="border-b-2 border-slate-500 pb-1 mb-3 h-8 relative">
              <span className="absolute bottom-0 left-0 text-slate-500 text-xs">Date</span>
            </div>
            <p className="text-slate-400 text-sm">Date:</p>
          </div>
        </div>

        {/* Client Section */}
        <div className="signature-section space-y-6">
          <div>
            <div className="border-b-2 border-slate-500 pb-1 mb-3 h-12 relative">
              <span className="absolute bottom-0 left-0 text-slate-500 text-xs">Signature</span>
            </div>
            <p className="text-slate-300 text-sm font-semibold">**Client:**</p>
          </div>

          <div>
            <div className="border-b-2 border-slate-500 pb-1 mb-3 h-8 relative">
              <span className="absolute bottom-0 left-0 text-slate-500 text-xs">Date</span>
            </div>
            <p className="text-slate-400 text-sm">Date:</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Contract Table Component (for parties information)
const ContractTable = ({ content }) => {
  // Parse table content - expecting format like "Field|Service Provider|Client"
  const rows = content.split('\n').filter(row => row.trim() && row.includes('|'));

  if (rows.length === 0) {
    return <div className="text-slate-400 mb-4">No table data available</div>;
  }

  return (
    <div className="contract-table bg-slate-900/30 border border-slate-600 rounded-lg overflow-hidden mb-6">
      <table className="w-full">
        <thead>
          <tr className="bg-slate-800/70">
            <th className="px-4 py-3 text-left text-blue-300 font-semibold border-b border-slate-600">Field</th>
            <th className="px-4 py-3 text-left text-blue-300 font-semibold border-b border-slate-600">Service Provider</th>
            <th className="px-4 py-3 text-left text-blue-300 font-semibold border-b border-slate-600">Client</th>
          </tr>
        </thead>
        <tbody>
          {rows.slice(1).map((row, index) => {
            const cells = row.split('|').map(cell => cell.trim());

            return (
              <tr key={index} className="hover:bg-slate-800/30">
                <td className="px-4 py-3 border-b border-slate-700 font-semibold text-cyan-300 bg-slate-800/20">
                  {cells[0] || ''}
                </td>
                <td className="px-4 py-3 border-b border-slate-700 text-slate-300 font-['Poppins']">
                  {cells[1] || ''}
                </td>
                <td className="px-4 py-3 border-b border-slate-700 text-slate-300 font-['Poppins']">
                  {cells[2] || ''}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

// Plain Text Component
const PlainText = ({ content }) => (
  <p className="text-slate-400 mb-3 leading-relaxed">{content}</p>
);

// Main Formatted Contract Display Component
const FormattedContractDisplay = ({ content, language = 'English' }) => {
  if (!content) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-400">No contract content to display</p>
      </div>
    );
  }

  const parsedElements = parseContractContent(content);
  const textDirection = getTextDirection(language);
  const isRTL = textDirection === 'rtl';

  return (
    <div 
      className={`contract-display ${isRTL ? 'rtl' : 'ltr'}`}
      dir={textDirection}
      style={{ direction: textDirection }}
    >
      <div className="space-y-2">
        {parsedElements.map((element) => {
          switch (element.type) {
            case 'header':
              return <ContractHeader key={element.key} content={element.content} />;
            case 'section':
              return <SectionHeader key={element.key} content={element.content} />;
            case 'subsection':
              return <SubSectionHeader key={element.key} content={element.content} />;
            case 'paragraph':
              return <ContractParagraph key={element.key} content={element.content} />;
            case 'listItem':
              return <ContractListItem key={element.key} content={element.content} />;
            case 'clause':
              return <ContractClause key={element.key} content={element.content} />;
            case 'term':
              return <ContractTerm key={element.key} content={element.content} />;
            case 'financial':
              return <FinancialTerm key={element.key} content={element.content} />;
            case 'table':
              return <ContractTable key={element.key} content={element.content} />;
            case 'signature':
              return <SignatureBlock key={element.key} content={element.content} />;
            case 'text':
              return <PlainText key={element.key} content={element.content} />;
            default:
              return <PlainText key={element.key} content={element.content} />;
          }
        })}
      </div>
    </div>
  );
};

export default FormattedContractDisplay;

import React from 'react';
import { FiUsers, FiArrowRight } from 'react-icons/fi';
import FormInput from '../form/FormInput';
import FormSelect from '../form/FormSelect';
import { fieldLabels, jurisdictionOptions, languageOptions } from '../data/fieldLabels';

const Step1PartiesDetails = ({ formData, onChange, onNext, isValid }) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full mb-4 backdrop-blur-sm border border-white/10">
          <FiUsers className="w-6 h-6 text-blue-400 mr-2" />
          <span className="text-blue-400 font-semibold">Step 1 of 5</span>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-200 to-cyan-200 bg-clip-text text-transparent mb-2">
          Parties and Contract Details
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          Enter the basic information about both parties and contract fundamentals
        </p>
      </div>

      {/* Form */}
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8">
        <div className="space-y-8">
          {/* Service Provider Section */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Service Provider Information
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <FormInput
                {...fieldLabels.providerName}
                name="providerName"
                value={formData.providerName || ''}
                onChange={handleInputChange}
              />
              <FormInput
                {...fieldLabels.providerEmail}
                name="providerEmail"
                value={formData.providerEmail || ''}
                onChange={handleInputChange}
                type="email"
              />
            </div>
            <div className="mt-6">
              <FormInput
                {...fieldLabels.providerAddress}
                name="providerAddress"
                value={formData.providerAddress || ''}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* Client Section */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Client Information
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <FormInput
                {...fieldLabels.clientName}
                name="clientName"
                value={formData.clientName || ''}
                onChange={handleInputChange}
              />
              <FormInput
                {...fieldLabels.clientEmail}
                name="clientEmail"
                value={formData.clientEmail || ''}
                onChange={handleInputChange}
                type="email"
              />
            </div>
            <div className="mt-6">
              <FormInput
                {...fieldLabels.clientAddress}
                name="clientAddress"
                value={formData.clientAddress || ''}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* Contract Details Section */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
              <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
              Contract Details
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <FormInput
                {...fieldLabels.effectiveDate}
                name="effectiveDate"
                value={formData.effectiveDate || ''}
                onChange={handleInputChange}
                type="date"
              />
              <FormSelect
                {...fieldLabels.jurisdiction}
                name="jurisdiction"
                value={formData.jurisdiction || ''}
                onChange={handleInputChange}
                options={jurisdictionOptions}
              />
            </div>
            <div className="mt-6">
              <FormSelect
                {...fieldLabels.contractLanguage}
                name="contractLanguage"
                value={formData.contractLanguage || ''}
                onChange={handleInputChange}
                options={languageOptions}
              />
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-end mt-8 pt-6 border-t border-slate-700">
          <button
            onClick={onNext}
            disabled={!isValid}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
              isValid
                ? 'bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white shadow-lg hover:shadow-blue-500/25'
                : 'bg-slate-700 text-slate-400 cursor-not-allowed'
            }`}
          >
            <span>Continue to Work Scope</span>
            <FiArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step1PartiesDetails;

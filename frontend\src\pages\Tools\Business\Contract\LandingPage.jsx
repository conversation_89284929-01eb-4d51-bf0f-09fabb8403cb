import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FiFileText, 
  FiUsers, 
  FiClock, 
  FiDollarSign, 
  FiShield, 
  FiArrowRight,
  FiStar,
  FiCheckCircle
} from 'react-icons/fi';

const ServiceContractCard = () => {
  const navigate = useNavigate();

  const handleNavigateToContract = () => {
    navigate('/app/business/contract/service');
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4 py-8">
      {/* Header Section */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full mb-6 backdrop-blur-sm border border-white/10">
          <FiFileText className="w-8 h-8 text-blue-400 mr-3" />
          <span className="text-blue-400 font-semibold text-lg">Legal Contract Generator</span>
        </div>
        <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-blue-200 to-cyan-200 bg-clip-text text-transparent mb-4">
          Professional Contract Creation
        </h1>
        <p className="text-slate-400 text-lg max-w-2xl mx-auto leading-relaxed">
          Generate legally sound service contracts with AI-powered assistance. Professional, comprehensive, and tailored to your business needs.
        </p>
      </div>

      {/* Service Contract Card */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-sky-500/20 rounded-2xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
        
        <div className="relative bg-slate-900/80 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300 group-hover:transform group-hover:scale-[1.02]">
          {/* Card Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-500/20 rounded-xl border border-blue-400/30">
                <FiFileText className="w-8 h-8 text-blue-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white mb-1">Service Agreement Contract</h2>
                <p className="text-slate-400">Professional service contracts with AI assistance</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-2 text-yellow-400">
              <FiStar className="w-5 h-5 fill-current" />
              <span className="font-semibold">AI-Powered</span>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 gap-4 mb-8">
            <div className="flex items-center space-x-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/50">
              <FiUsers className="w-5 h-5 text-green-400" />
              <span className="text-slate-300">Parties & Contact Details</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/50">
              <FiFileText className="w-5 h-5 text-blue-400" />
              <span className="text-slate-300">Comprehensive Work Scope</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/50">
              <FiClock className="w-5 h-5 text-purple-400" />
              <span className="text-slate-300">Timeline & Milestones</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-slate-800/50 rounded-lg border border-slate-700/50">
              <FiDollarSign className="w-5 h-5 text-yellow-400" />
              <span className="text-slate-300">Payment Terms & Conditions</span>
            </div>
          </div>

          {/* Key Benefits */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiCheckCircle className="w-5 h-5 text-green-400 mr-2" />
              What You'll Get
            </h3>
            <div className="grid md:grid-cols-2 gap-3">
              <div className="flex items-center space-x-2 text-slate-300">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Legally compliant contract structure</span>
              </div>
              <div className="flex items-center space-x-2 text-slate-300">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Professional formatting & language</span>
              </div>
              <div className="flex items-center space-x-2 text-slate-300">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span>Customizable terms & conditions</span>
              </div>
              <div className="flex items-center space-x-2 text-slate-300">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span>Optional protective clauses</span>
              </div>
            </div>
          </div>

          {/* Process Steps Preview */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FiShield className="w-5 h-5 text-cyan-400 mr-2" />
              Simple 5-Step Process
            </h3>
            <div className="flex flex-wrap gap-2">
              {[
                'Parties & Details',
                'Work Scope',
                'Timeline & Payment',
                'Optional Clauses',
                'Generate Contract'
              ].map((step, index) => (
                <div key={index} className="px-3 py-1 bg-slate-800/50 border border-slate-600/50 rounded-full text-sm text-slate-300">
                  {index + 1}. {step}
                </div>
              ))}
            </div>
          </div>

          {/* Action Button */}
          <button
            onClick={handleNavigateToContract}
            className="w-full bg-gradient-to-r from-blue-600 via-cyan-600 to-sky-600 hover:from-blue-500 hover:via-cyan-500 hover:to-sky-500 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 group-hover:shadow-lg group-hover:shadow-blue-500/25"
          >
            <span className="text-lg">Create Service Contract</span>
            <FiArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
          </button>

          {/* Disclaimer */}
          <p className="text-xs text-slate-500 text-center mt-4">
            AI-generated contracts should be reviewed by legal professionals before use
          </p>
        </div>
      </div>
    </div>
  );
};

const ContractLandingPage = () => {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        <ServiceContractCard />
      </div>
    </div>
  );
};

export default ContractLandingPage;

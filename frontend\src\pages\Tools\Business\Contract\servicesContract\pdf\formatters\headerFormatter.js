// PDF Header Formatter
// Specialized formatting for contract headers

/**
 * Render contract main header with underline
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Header content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderContractHeader = (doc, content, currentY, config) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;

  // Set header font - using Poppins-like styling
  doc.setFont(config.fonts.bold);
  doc.setFontSize(20);
  doc.setTextColor(config.colors.primary);

  // Calculate text width and position
  const textWidth = doc.getTextWidth(content);
  const xPosition = isRTL 
    ? pageWidth - config.margins.right - textWidth
    : (pageWidth - textWidth) / 2; // Center align

  // Render header text
  doc.text(content, xPosition, currentY);

  // Add thick underline (matching web display)
  const underlineY = currentY + 3;
  const underlineStartX = isRTL 
    ? pageWidth - config.margins.right - textWidth - 15
    : xPosition - 15;
  const underlineEndX = isRTL 
    ? pageWidth - config.margins.right + 15
    : xPosition + textWidth + 15;

  // Double underline effect
  doc.setDrawColor(config.colors.primary);
  doc.setLineWidth(2);
  doc.line(underlineStartX, underlineY, underlineEndX, underlineY);
  doc.setLineWidth(1);
  doc.line(underlineStartX, underlineY + 2, underlineEndX, underlineY + 2);

  return currentY + 30;
};

/**
 * Render contract section header with underline
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Section content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @param {boolean} isSubsection - Whether this is a subsection
 * @returns {number} - New Y position
 */
export const renderContractSection = (doc, content, currentY, config, isSubsection = false) => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const isRTL = doc.internal.isRtl;

  // Set section font
  doc.setFont(config.fonts.bold);
  doc.setFontSize(isSubsection ? 12 : 16);
  doc.setTextColor(isSubsection ? config.colors.text : config.colors.secondary);

  // Calculate position
  const xPosition = isRTL 
    ? pageWidth - config.margins.right
    : config.margins.left;

  // Add section marker (colored bar) - matching web display
  if (!isSubsection) {
    const barX = isRTL 
      ? pageWidth - config.margins.right + 2
      : config.margins.left - 5;
    
    doc.setFillColor(config.colors.secondary);
    doc.rect(barX, currentY - 5, 4, 10, 'F');
  }

  // Render section text
  const textOptions = isRTL ? { align: 'right' } : {};
  doc.text(content, xPosition, currentY, textOptions);

  // Add underline for main sections (matching web display)
  if (!isSubsection) {
    const textWidth = doc.getTextWidth(content);
    const underlineY = currentY + 3;
    const underlineStartX = isRTL 
      ? pageWidth - config.margins.right - textWidth
      : config.margins.left;
    const underlineEndX = isRTL 
      ? pageWidth - config.margins.right
      : config.margins.left + textWidth;

    doc.setDrawColor(config.colors.secondary);
    doc.setLineWidth(1.5);
    doc.line(underlineStartX, underlineY, underlineEndX, underlineY);
  }

  return currentY + (isSubsection ? 18 : 25);
};

/**
 * Render sub-section header
 * @param {jsPDF} doc - PDF document
 * @param {string} content - Subsection content
 * @param {number} currentY - Current Y position
 * @param {Object} config - PDF configuration
 * @returns {number} - New Y position
 */
export const renderSubSection = (doc, content, currentY, config) => {
  return renderContractSection(doc, content, currentY, config, true);
};
